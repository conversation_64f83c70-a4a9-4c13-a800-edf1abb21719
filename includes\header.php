<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?>Flori Construction Ltd</title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : 'Professional construction services in London. Civil Engineering, Groundworks, RC Frames, Basements, and Hard Landscaping.'; ?>">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <!-- Top Bar -->
        <div class="top-bar bg-dark text-white py-2">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="contact-info">
                            <span class="me-3">
                                <i class="fas fa-phone"></i> 
                                <a href="tel:02089147883" class="text-white text-decoration-none">0208 914 7883</a>
                            </span>
                            <span>
                                <i class="fas fa-envelope"></i> 
                                <a href="mailto:<EMAIL>" class="text-white text-decoration-none"><EMAIL></a>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="social-links">
                            <a href="<?php echo getSetting('facebook_url'); ?>" class="text-white me-2" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="<?php echo getSetting('instagram_url'); ?>" class="text-white me-2" target="_blank">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="<?php echo getSetting('youtube_url'); ?>" class="text-white me-2" target="_blank">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="<?php echo getSetting('linkedin_url'); ?>" class="text-white" target="_blank">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
            <div class="container">
                <a class="navbar-brand" href="index.php">
                    <img src="assets/images/logo.png" alt="Flori Construction Ltd" height="60">
                </a>
                
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>" href="index.php">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'about.php' ? 'active' : ''; ?>" href="about.php">About Us</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo basename($_SERVER['PHP_SELF']) == 'services.php' ? 'active' : ''; ?>" href="#" id="servicesDropdown" role="button" data-bs-toggle="dropdown">
                                Our Services
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="services.php">All Services</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <?php
                                $services = getServices('active');
                                foreach ($services as $service) {
                                    echo '<li><a class="dropdown-item" href="service-detail.php?id=' . $service['id'] . '">' . htmlspecialchars($service['title']) . '</a></li>';
                                }
                                ?>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo basename($_SERVER['PHP_SELF']) == 'projects.php' ? 'active' : ''; ?>" href="#" id="projectsDropdown" role="button" data-bs-toggle="dropdown">
                                Our Projects
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="projects.php">All Projects</a></li>
                                <li><a class="dropdown-item" href="projects.php?status=completed">Completed Projects</a></li>
                                <li><a class="dropdown-item" href="projects.php?status=ongoing">Ongoing Projects</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'media.php' ? 'active' : ''; ?>" href="media.php">Media</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : ''; ?>" href="contact.php">Contact Us</a>
                        </li>
                    </ul>
                    
                    <div class="ms-3">
                        <a href="contact.php" class="btn btn-primary">Get Quote</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Content Starts Here -->
