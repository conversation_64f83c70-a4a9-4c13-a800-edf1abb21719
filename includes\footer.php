    <!-- Footer -->
    <footer class="footer bg-dark text-white py-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-widget">
                        <img src="assets/images/logo-white.png" alt="Flori Construction Ltd" class="mb-3" height="50">
                        <p class="mb-3">Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.</p>
                        <div class="social-links">
                            <a href="<?php echo getSetting('facebook_url'); ?>" class="text-white me-3" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="<?php echo getSetting('instagram_url'); ?>" class="text-white me-3" target="_blank">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <a href="<?php echo getSetting('youtube_url'); ?>" class="text-white me-3" target="_blank">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <a href="<?php echo getSetting('linkedin_url'); ?>" class="text-white" target="_blank">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-widget">
                        <h5 class="mb-3">Company</h5>
                        <ul class="list-unstyled">
                            <li><a href="index.php" class="text-white-50 text-decoration-none">Home</a></li>
                            <li><a href="about.php" class="text-white-50 text-decoration-none">About Us</a></li>
                            <li><a href="services.php" class="text-white-50 text-decoration-none">Our Services</a></li>
                            <li><a href="projects.php" class="text-white-50 text-decoration-none">Our Projects</a></li>
                            <li><a href="media.php" class="text-white-50 text-decoration-none">Media</a></li>
                            <li><a href="contact.php" class="text-white-50 text-decoration-none">Contact Us</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-widget">
                        <h5 class="mb-3">Services</h5>
                        <ul class="list-unstyled">
                            <?php
                            $footerServices = getServices('active');
                            foreach (array_slice($footerServices, 0, 5) as $service) {
                                echo '<li><a href="service-detail.php?id=' . $service['id'] . '" class="text-white-50 text-decoration-none">' . htmlspecialchars($service['title']) . '</a></li>';
                            }
                            ?>
                        </ul>
                    </div>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="footer-widget">
                        <h5 class="mb-3">Contact Info</h5>
                        <div class="contact-info">
                            <div class="mb-2">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                <span><?php echo getSetting('company_address'); ?></span>
                            </div>
                            <div class="mb-2">
                                <i class="fas fa-phone me-2"></i>
                                <a href="tel:<?php echo str_replace(' ', '', getSetting('company_phone')); ?>" class="text-white-50 text-decoration-none">
                                    <?php echo getSetting('company_phone'); ?>
                                </a>
                            </div>
                            <div class="mb-2">
                                <i class="fas fa-mobile-alt me-2"></i>
                                <a href="tel:<?php echo str_replace(' ', '', getSetting('company_mobile')); ?>" class="text-white-50 text-decoration-none">
                                    <?php echo getSetting('company_mobile'); ?>
                                </a>
                            </div>
                            <div class="mb-2">
                                <i class="fas fa-envelope me-2"></i>
                                <a href="mailto:<?php echo getSetting('company_email'); ?>" class="text-white-50 text-decoration-none">
                                    <?php echo getSetting('company_email'); ?>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; <?php echo date('Y'); ?> Flori Construction Ltd. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="privacy-policy.php" class="text-white-50 text-decoration-none me-3">Privacy Policy</a>
                    <a href="terms.php" class="text-white-50 text-decoration-none">Terms & Conditions</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button class="btn-back-to-top" id="backToTop">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Custom JS -->
    <script src="js/main.js"></script>
    
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });
        
        // Back to top button
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'block';
            } else {
                backToTop.style.display = 'none';
            }
        });
        
        document.getElementById('backToTop').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>
